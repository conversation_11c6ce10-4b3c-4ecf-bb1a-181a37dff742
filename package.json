{"name": "be-product-master", "version": "0.0.1", "description": "", "author": "Social Unlimited Group", "private": true, "license": "UNLICENSED", "packageManager": "pnpm@10.0.0", "engines": {"node": ">=22", "pnpm": ">=10", "npm": "please-use-pnpm", "yarn": "please-use-pnpm"}, "scripts": {"build": "nest build", "prebuild": "pnpm run graphql:generate && pnpm run codegen", "predev": "pnpm run graphql:generate && pnpm run codegen", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "prepare": "husky", "test": "jest", "test:hooks": ".husky/pre-commit", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "env": "node -e \"const fs = require('fs'); if (!fs.existsSync('.env') && fs.existsSync('.env.example')) { fs.copyFileSync('.env.example', '.env'); console.log('.env file created from example'); }\"", "docker:up": "docker compose up -d", "docker:down": "docker compose down", "docker:clean": "docker compose down -v", "prisma:db:push": "prisma db push", "prisma:db:seed": "prisma db seed", "prisma:studio": "prisma studio", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:create": "prisma migrate dev --create-only", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:migrate:reset": "prisma migrate reset", "prisma:migrate:status": "prisma migrate status", "prisma:migrate:resolve": "prisma migrate resolve", "prisma:generate": "prisma generate", "prisma:generate:watch": "prisma generate --watch", "prisma:reset": "prisma migrate reset && prisma generate", "graphql:generate": "ts-node generate-typings.ts", "graphql:generate:watch": "ts-node generate-typings.ts --watch", "codegen:watch": "graphql-codegen --watch", "codegen": "graphql-codegen", "shopify:types": "graphql-codegen"}, "dependencies": {"@aws-sdk/client-s3": "^3.848.0", "@aws-sdk/client-sqs": "^3.848.0", "@graphql-yoga/nestjs-federation": "^3.15.1", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/graphql": "^13.1.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/microservices": "^11.1.6", "@nestjs/platform-express": "^11.1.5", "@prisma/client": "^6.13.0", "@shopify/admin-api-client": "^1.1.1", "@shopify/shopify-api": "^11.14.1", "@ssut/nestjs-sqs": "^3.0.1", "@sw-ecom360/shopify-admin-api": "^1.2.10", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "graphql": "^16.10.0", "graphql-request": "^5.2.0", "graphql-scalars": "^1.24.2", "graphql-yoga": "^5.15.1", "json-bigint-patch": "^0.0.8", "lodash": "^4.17.21", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sqs-consumer": "^11.6.0", "uuid": "^10.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-resolvers": "^4.5.1", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@parcel/watcher": "^2.5.1", "@shopify/api-codegen-preset": "^1.1.9", "@sw-ecom360/lint-config": "^1.1.13", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.20", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "graphql-config": "^5.1.5", "husky": "^9.1.7", "jest": "^29.7.0", "prettier": "^3.4.2", "prisma": "^6.13.0", "prisma-json-types-generator": "^3.5.2", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-morph": "^25.0.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "prisma": {"schema": "./prisma", "prismaSchemaFolder": "prisma/models"}, "pnpm": {"onlyBuiltDependencies": ["@apollo/protobufjs", "@nestjs/core", "@parcel/watcher", "@prisma/client", "@prisma/engines", "@swc/core", "core-js", "esbuild", "prisma", "unrs-resolver"]}}