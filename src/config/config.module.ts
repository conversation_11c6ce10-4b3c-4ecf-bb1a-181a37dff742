import { Global, Module } from "@nestjs/common";
import {
  ConfigFactory,
  ConfigModule as NestConfigModule,
} from "@nestjs/config";
import appConfig from "./configuration/app.config";
import awsConfig from "./configuration/aws.config";
import graphqlConfig from "./configuration/graphql.config";
import { validate } from "./validation";

const isProduction = process.env.NODE_ENV === "production";

const envFilePath = isProduction
  ? undefined
  : [
      `.env.${process.env.NODE_ENV}.local`,
      `.env.${process.env.NODE_ENV}`,
      ".env.local",
      ".env",
    ];

const load: ConfigFactory[] = [appConfig, awsConfig, graphqlConfig];

@Global()
@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      envFilePath,
      load,
      validate,
      ignoreEnvFile: isProduction,
    }),
  ],
})
export class ConfigModule {}
