import { PipeTransform, Injectable, BadRequestException } from "@nestjs/common";

@Injectable()
export class FileValidationPipe implements PipeTransform {
  transform(file: File) {
    if (!file) {
      throw new BadRequestException("File is required");
    }

    // Basic file existence check
    if (!file.name || !file.type) {
      throw new BadRequestException("Invalid file format");
    }

    // File size validation (10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      throw new BadRequestException(
        "File size too large. Maximum 10MB allowed.",
      );
    }

    // File type validation
    const validMimeTypes = [
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
      "application/vnd.ms-excel", // .xls
    ];

    const validExtensions = [".xlsx", ".xls"];
    const hasValidExtension = validExtensions.some((ext) =>
      file.name.toLowerCase().endsWith(ext),
    );
    const hasValidMimeType = validMimeTypes.includes(file.type);

    if (!hasValidExtension || !hasValidMimeType) {
      throw new BadRequestException(
        "Invalid file type. Only Excel files (.xlsx, .xls) are allowed.",
      );
    }

    return file;
  }
}
