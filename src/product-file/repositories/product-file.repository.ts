import { Injectable } from "@nestjs/common";
import { plainToInstance } from "class-transformer";
import { PrismaService } from "../../common/services/prisma.service";
import { CreateProductFileDto } from "../dto/create-product-file.dto";
import { ProductFileResponseDto } from "../dto/product-file-response.dto";
import { ProductFileStatus } from "../types/product-file-status.types";

@Injectable()
export class ProductFileRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateProductFileDto): Promise<ProductFileResponseDto> {
    const productFile = await this.prisma.productFile.create({
      data: {
        name: data.name,
        s3Key: data.s3Key,
        s3Url: data.s3Url,
        status: data.status || ProductFileStatus.UPLOADED,
      },
    });

    return plainToInstance(ProductFileResponseDto, productFile);
  }

  async findById(id: number): Promise<ProductFileResponseDto | null> {
    const productFile = await this.prisma.productFile.findUnique({
      where: { id },
    });

    return productFile
      ? plainToInstance(ProductFileResponseDto, productFile)
      : null;
  }

  async findAll(): Promise<ProductFileResponseDto[]> {
    const productFiles = await this.prisma.productFile.findMany({
      orderBy: { createdAt: "desc" },
    });

    return plainToInstance(ProductFileResponseDto, productFiles);
  }

  async updateStatus(
    id: number,
    status: ProductFileStatus,
  ): Promise<ProductFileResponseDto> {
    const productFile = await this.prisma.productFile.update({
      where: { id },
      data: { status },
    });

    return plainToInstance(ProductFileResponseDto, productFile);
  }

  async delete(id: number): Promise<void> {
    await this.prisma.productFile.delete({
      where: { id },
    });
  }
}
