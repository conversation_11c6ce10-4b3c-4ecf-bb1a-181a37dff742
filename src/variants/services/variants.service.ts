import { Injectable } from "@nestjs/common";
import { PaginationInput } from "../../common/dto/pagination.input";
import { PaginationService } from "../../common/services/pagination.service";
import { PaginatedVariantsResponseDto } from "../dto/paginated-variants-response.dto";
import { VariantWhereDto } from "../dto/variant-where.dto";
import { VariantRepository } from "../repositories/variant.repository";

@Injectable()
export class VariantsService {
  // private readonly logger = new Logger(VariantsService.name);

  constructor(
    private readonly variantRepository: VariantRepository,
    private readonly paginationService: PaginationService,
  ) {}

  findAll(whereInput?: VariantWhereDto) {
    return this.variantRepository.findAll(whereInput);
  }

  async findAllPaginated(
    pagination: PaginationInput,
    whereInput: VariantWhereDto,
  ): Promise<PaginatedVariantsResponseDto> {
    const paginationParams =
      this.paginationService.getPaginationParams(pagination);

    const { totalCount, variants: data } =
      await this.variantRepository.findAllPaginated(
        paginationParams,
        whereInput,
      );
    const meta = this.paginationService.getPaginationMeta(
      pagination,
      totalCount,
    );

    return { meta, data };
  }

  async searchVariants(
    pagination: PaginationInput,
    query: string,
  ): Promise<PaginatedVariantsResponseDto> {
    const paginationParams =
      this.paginationService.getPaginationParams(pagination);

    const { totalCount, variants: data } =
      await this.variantRepository.searchVariants(paginationParams, query);

    const meta = this.paginationService.getPaginationMeta(
      pagination,
      totalCount,
    );

    return { meta, data };
  }

  findOne(id: number) {
    return this.variantRepository.findOne(id);
  }

  remove(id: number) {
    return this.variantRepository.remove(id);
  }

  doesSkuExist(sku: string) {
    return this.variantRepository.doesSkuExist(sku);
  }
}
