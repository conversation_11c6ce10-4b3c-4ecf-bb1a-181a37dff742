import { UsePipes, ValidationPipe } from "@nestjs/common";
import { Args, Query, Resolver } from "@nestjs/graphql";
import { PaginationInput } from "../common/dto/pagination.input";
import { VariantWhereDto } from "./dto/variant-where.dto";
import { VariantsService } from "./services/variants.service";

@Resolver("Variant")
@UsePipes(new ValidationPipe({ transform: true }))
export class VariantsResolver {
  constructor(private readonly variantService: VariantsService) {}

  @Query("variants")
  async findAll(
    @Args("pagination") pagination: PaginationInput,
    @Args("where") where: VariantWhereDto,
  ) {
    //: Promise<GraphQL.VariantConnection>
    return this.variantService.findAllPaginated(pagination, where);
  }

  @Query("searchVariants")
  async searchVariants(
    @Args("pagination") pagination: PaginationInput,
    @Args("query") query: string,
  ) {
    //: Promise<GraphQL.VariantConnection>
    return this.variantService.searchVariants(pagination, query);
  }

  @Query("variant")
  findOneVariant(@Args("id") id: number) {
    //: Promise<GraphQL.Variant>
    return this.variantService.findOne(id);
  }
}
