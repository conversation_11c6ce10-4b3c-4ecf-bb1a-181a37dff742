import { Module } from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { ClientsModule, Transport } from "@nestjs/microservices";
import microserviceConfig from "../config/configuration/microservice.config";
import { PaginationService } from "./services/pagination.service";
import { PrismaService } from "./services/prisma.service";
import { QueryMapperService } from "./services/query-mapper.service";
import { ShopService, SHOP_SERVICE_NAME } from "./services/shop.service";

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: SHOP_SERVICE_NAME,
        inject: [microserviceConfig.KEY],
        useFactory(microserviceConf: ConfigType<typeof microserviceConfig>) {
          return {
            transport: Transport.TCP,
            options: {
              port: microserviceConf.clients.shopService.port,
            },
          };
        },
      },
    ]),
  ],
  providers: [
    PaginationService,
    QueryMapperService,
    PrismaService,
    ShopService,
  ],
  exports: [PaginationService, QueryMapperService, PrismaService, ShopService],
})
export class CommonModule {}
