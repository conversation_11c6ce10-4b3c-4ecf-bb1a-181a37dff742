import { Module } from "@nestjs/common";
import { ConfigModule, ConfigType } from "@nestjs/config";
import { SqsModule } from "@ssut/nestjs-sqs";
import awsConfig from "../config/configuration/aws.config";

@Module({
  imports: [
    ConfigModule.forFeature(awsConfig),
    SqsModule.registerAsync({
      inject: [awsConfig.KEY],
      useFactory: (cfg: ConfigType<typeof awsConfig>) => {
        if (!cfg.productsQueueUrl || !cfg.fileUploadQueueUrl) {
          throw new Error(
            "AWS_SQS_PRODUCTS_QUEUE_URL and AWS_SQS_PRODUCT_FILE_UPLOAD_QUEUE_URL must be set",
          );
        }
        return {
          consumers: [
            {
              name: "products",
              queueUrl: cfg.productsQueueUrl,
              region: cfg.region,
              waitTimeSeconds: 20,
            },
            {
              name: "excel-products",
              queueUrl: cfg.productsQueueUrl,
              region: cfg.region,
              waitTimeSeconds: 20,
            },
            {
              name: "product-file-upload",
              queueUrl: cfg.fileUploadQueueUrl,
              region: cfg.region,
              waitTimeSeconds: 20,
            },
          ],
          producers: [
            {
              name: "products",
              queueUrl: cfg.productsQueueUrl,
              region: cfg.region,
            },
            {
              name: "excel-products",
              queueUrl: cfg.productsQueueUrl,
              region: cfg.region,
            },
            {
              name: "product-file-upload",
              queueUrl: cfg.fileUploadQueueUrl,
              region: cfg.region,
            },
          ],
        };
      },
    }),
  ],
  exports: [SqsModule],
})
export class AppSqsModule {}
