import { <PERSON>, Get, Inject, Logger } from "@nestjs/common";
import {
  ClientProxy,
  ClientProxyFactory,
  Transport,
} from "@nestjs/microservices";
import { firstValueFrom, timeout } from "rxjs";

@Controller("internal/pricing")
export class InterserviceController {
  private readonly logger = new Logger(InterserviceController.name);

  constructor(
    @Inject("PRICING_SERVICE") private readonly pricingClient: ClientProxy,
  ) {}

  @Get("health")
  async health() {
    try {
      const result = await firstValueFrom(
        this.pricingClient.send("health_check", {}).pipe(timeout(5000)),
      );
      return result;
    } catch (err) {
      this.logger.error(`Pricing service health_check failed: ${String(err)}`);
      throw err;
    }
  }

  @Get("debug/direct-ip")
  async testDirectIp() {
    const ips = ["**********", "**********"];
    const results: Array<{
      ip: string;
      status: string;
      result?: unknown;
      error?: string;
    }> = [];

    for (const ip of ips) {
      try {
        const client = ClientProxyFactory.create({
          transport: Transport.TCP,
          options: { host: ip, port: 3003 },
        });

        const result = await firstValueFrom(
          client.send("health_check", {}).pipe(timeout(5000)),
        );
        results.push({ ip, status: "success", result });
      } catch (error) {
        results.push({ ip, status: "error", error: error.message });
      }
    }

    return results;
  }
}
